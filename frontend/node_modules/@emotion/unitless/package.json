{"name": "@emotion/unitless", "version": "0.10.0", "description": "An object of css properties that don't accept values with units", "main": "dist/emotion-unitless.cjs.js", "module": "dist/emotion-unitless.esm.js", "types": "dist/emotion-unitless.cjs.d.ts", "license": "MIT", "repository": "https://github.com/emotion-js/emotion/tree/main/packages/unitless", "publishConfig": {"access": "public"}, "files": ["src", "dist"], "exports": {".": {"types": {"import": "./dist/emotion-unitless.cjs.mjs", "default": "./dist/emotion-unitless.cjs.js"}, "module": "./dist/emotion-unitless.esm.js", "import": "./dist/emotion-unitless.cjs.mjs", "default": "./dist/emotion-unitless.cjs.js"}, "./package.json": "./package.json"}}