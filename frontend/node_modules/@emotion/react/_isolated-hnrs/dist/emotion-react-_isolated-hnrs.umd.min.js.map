{"version": 3, "file": "emotion-react-_isolated-hnrs.umd.min.js", "sources": ["../../../../node_modules/react-is/cjs/react-is.production.min.js", "../../../../node_modules/react-is/index.js", "../../../../node_modules/hoist-non-react-statics/dist/hoist-non-react-statics.cjs.js", "../../src/_isolated-hnrs.ts"], "sourcesContent": ["/** @license React v16.12.0\n * react-is.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';Object.defineProperty(exports,\"__esModule\",{value:!0});\nvar b=\"function\"===typeof Symbol&&Symbol.for,c=b?Symbol.for(\"react.element\"):60103,d=b?Symbol.for(\"react.portal\"):60106,e=b?Symbol.for(\"react.fragment\"):60107,f=b?Symbol.for(\"react.strict_mode\"):60108,g=b?Symbol.for(\"react.profiler\"):60114,h=b?Symbol.for(\"react.provider\"):60109,k=b?Symbol.for(\"react.context\"):60110,l=b?Symbol.for(\"react.async_mode\"):60111,m=b?Symbol.for(\"react.concurrent_mode\"):60111,n=b?Symbol.for(\"react.forward_ref\"):60112,p=b?Symbol.for(\"react.suspense\"):60113,q=b?Symbol.for(\"react.suspense_list\"):\n60120,r=b?Symbol.for(\"react.memo\"):60115,t=b?Symbol.for(\"react.lazy\"):60116,v=b?Symbol.for(\"react.fundamental\"):60117,w=b?Symbol.for(\"react.responder\"):60118,x=b?Symbol.for(\"react.scope\"):60119;function y(a){if(\"object\"===typeof a&&null!==a){var u=a.$$typeof;switch(u){case c:switch(a=a.type,a){case l:case m:case e:case g:case f:case p:return a;default:switch(a=a&&a.$$typeof,a){case k:case n:case t:case r:case h:return a;default:return u}}case d:return u}}}function z(a){return y(a)===m}\nexports.typeOf=y;exports.AsyncMode=l;exports.ConcurrentMode=m;exports.ContextConsumer=k;exports.ContextProvider=h;exports.Element=c;exports.ForwardRef=n;exports.Fragment=e;exports.Lazy=t;exports.Memo=r;exports.Portal=d;exports.Profiler=g;exports.StrictMode=f;exports.Suspense=p;\nexports.isValidElementType=function(a){return\"string\"===typeof a||\"function\"===typeof a||a===e||a===m||a===g||a===f||a===p||a===q||\"object\"===typeof a&&null!==a&&(a.$$typeof===t||a.$$typeof===r||a.$$typeof===h||a.$$typeof===k||a.$$typeof===n||a.$$typeof===v||a.$$typeof===w||a.$$typeof===x)};exports.isAsyncMode=function(a){return z(a)||y(a)===l};exports.isConcurrentMode=z;exports.isContextConsumer=function(a){return y(a)===k};exports.isContextProvider=function(a){return y(a)===h};\nexports.isElement=function(a){return\"object\"===typeof a&&null!==a&&a.$$typeof===c};exports.isForwardRef=function(a){return y(a)===n};exports.isFragment=function(a){return y(a)===e};exports.isLazy=function(a){return y(a)===t};exports.isMemo=function(a){return y(a)===r};exports.isPortal=function(a){return y(a)===d};exports.isProfiler=function(a){return y(a)===g};exports.isStrictMode=function(a){return y(a)===f};exports.isSuspense=function(a){return y(a)===p};\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-is.production.min.js');\n} else {\n  module.exports = require('./cjs/react-is.development.js');\n}\n", "'use strict';\n\nvar reactIs = require('react-is');\n\n/**\n * Copyright 2015, Yahoo! Inc.\n * Copyrights licensed under the New BSD License. See the accompanying LICENSE file for terms.\n */\nvar REACT_STATICS = {\n  childContextTypes: true,\n  contextType: true,\n  contextTypes: true,\n  defaultProps: true,\n  displayName: true,\n  getDefaultProps: true,\n  getDerivedStateFromError: true,\n  getDerivedStateFromProps: true,\n  mixins: true,\n  propTypes: true,\n  type: true\n};\nvar KNOWN_STATICS = {\n  name: true,\n  length: true,\n  prototype: true,\n  caller: true,\n  callee: true,\n  arguments: true,\n  arity: true\n};\nvar FORWARD_REF_STATICS = {\n  '$$typeof': true,\n  render: true,\n  defaultProps: true,\n  displayName: true,\n  propTypes: true\n};\nvar MEMO_STATICS = {\n  '$$typeof': true,\n  compare: true,\n  defaultProps: true,\n  displayName: true,\n  propTypes: true,\n  type: true\n};\nvar TYPE_STATICS = {};\nTYPE_STATICS[reactIs.ForwardRef] = FORWARD_REF_STATICS;\n\nfunction getStatics(component) {\n  if (reactIs.isMemo(component)) {\n    return MEMO_STATICS;\n  }\n\n  return TYPE_STATICS[component['$$typeof']] || REACT_STATICS;\n}\n\nvar defineProperty = Object.defineProperty;\nvar getOwnPropertyNames = Object.getOwnPropertyNames;\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\nvar getPrototypeOf = Object.getPrototypeOf;\nvar objectPrototype = Object.prototype;\nfunction hoistNonReactStatics(targetComponent, sourceComponent, blacklist) {\n  if (typeof sourceComponent !== 'string') {\n    // don't hoist over string (html) components\n    if (objectPrototype) {\n      var inheritedComponent = getPrototypeOf(sourceComponent);\n\n      if (inheritedComponent && inheritedComponent !== objectPrototype) {\n        hoistNonReactStatics(targetComponent, inheritedComponent, blacklist);\n      }\n    }\n\n    var keys = getOwnPropertyNames(sourceComponent);\n\n    if (getOwnPropertySymbols) {\n      keys = keys.concat(getOwnPropertySymbols(sourceComponent));\n    }\n\n    var targetStatics = getStatics(targetComponent);\n    var sourceStatics = getStatics(sourceComponent);\n\n    for (var i = 0; i < keys.length; ++i) {\n      var key = keys[i];\n\n      if (!KNOWN_STATICS[key] && !(blacklist && blacklist[key]) && !(sourceStatics && sourceStatics[key]) && !(targetStatics && targetStatics[key])) {\n        var descriptor = getOwnPropertyDescriptor(sourceComponent, key);\n\n        try {\n          // Avoid failures from read-only properties\n          defineProperty(targetComponent, key, descriptor);\n        } catch (e) {}\n      }\n    }\n  }\n\n  return targetComponent;\n}\n\nmodule.exports = hoistNonReactStatics;\n", "// this file isolates this package that is not tree-shakeable\n// and allows it to be dropped - if it stays unused\n// it happens thanks to sideEffects: false in our package.json\nimport hoistNonReactStatics from 'hoist-non-react-statics'\n\n// have to wrap it in a proxy function because Rollup is too damn smart\n// and if this module doesn't actually contain any logic of its own\n// then Rollup just use 'hoist-non-react-statics' directly in other chunks\nexport default <\n  T extends React.ComponentType<any>,\n  S extends React.ComponentType<any>\n>(\n  targetComponent: T,\n  sourceComponent: S\n) => hoistNonReactStatics(targetComponent, sourceComponent)\n"], "names": ["Object", "defineProperty", "exports", "value", "b", "Symbol", "for", "c", "d", "e", "f", "g", "h", "k", "l", "m", "n", "p", "q", "r", "t", "v", "w", "x", "y", "a", "u", "$$typeof", "type", "z", "typeOf", "AsyncMode", "ConcurrentMode", "ContextConsumer", "ContextProvider", "Element", "ForwardRef", "Lazy", "Memo", "Portal", "Profiler", "StrictMode", "Suspense", "isValidElementType", "isAsyncMode", "isConcurrentMode", "isContextConsumer", "isContextProvider", "isElement", "isFragment", "isLazy", "isPortal", "isProfiler", "isSuspense", "module", "require$$0", "REACT_STATICS", "childContextTypes", "contextType", "contextTypes", "defaultProps", "displayName", "getDefaultProps", "getDerivedStateFromError", "getDerivedStateFromProps", "mixins", "propTypes", "KNOWN_STATICS", "name", "length", "prototype", "caller", "callee", "arguments", "arity", "MEMO_STATICS", "compare", "TYPE_STATICS", "getStatics", "component", "reactIs", "isMemo", "render", "getOwnPropertyNames", "getOwnPropertySymbols", "getOwnPropertyDescriptor", "getPrototypeOf", "objectPrototype", "hoistNonReactStatics_cjs", "hoistNonReactStatics", "targetComponent", "sourceComponent", "blacklist", "inheritedComponent", "keys", "concat", "targetStatics", "sourceStatics", "i", "key", "descriptor"], "mappings": "+eASaA,OAAOC,eAAeC,EAAQ,aAAa,CAACC,OAAM,IAC/D,IAAIC,EAAE,mBAAoBC,QAAQA,OAAOC,IAAIC,EAAEH,EAAEC,OAAOC,IAAI,iBAAiB,MAAME,EAAEJ,EAAEC,OAAOC,IAAI,gBAAgB,MAAMG,EAAEL,EAAEC,OAAOC,IAAI,kBAAkB,MAAMI,EAAEN,EAAEC,OAAOC,IAAI,qBAAqB,MAAMK,EAAEP,EAAEC,OAAOC,IAAI,kBAAkB,MAAMM,EAAER,EAAEC,OAAOC,IAAI,kBAAkB,MAAMO,EAAET,EAAEC,OAAOC,IAAI,iBAAiB,MAAMQ,EAAEV,EAAEC,OAAOC,IAAI,oBAAoB,MAAMS,EAAEX,EAAEC,OAAOC,IAAI,yBAAyB,MAAMU,EAAEZ,EAAEC,OAAOC,IAAI,qBAAqB,MAAMW,EAAEb,EAAEC,OAAOC,IAAI,kBAAkB,MAAMY,EAAEd,EAAEC,OAAOC,IAAI,uBACpf,MAAMa,EAAEf,EAAEC,OAAOC,IAAI,cAAc,MAAMc,EAAEhB,EAAEC,OAAOC,IAAI,cAAc,MAAMe,EAAEjB,EAAEC,OAAOC,IAAI,qBAAqB,MAAMgB,EAAElB,EAAEC,OAAOC,IAAI,mBAAmB,MAAMiB,EAAEnB,EAAEC,OAAOC,IAAI,eAAe,MAAM,SAASkB,EAAEC,GAAG,GAAG,iBAAkBA,GAAG,OAAOA,EAAE,CAAC,IAAIC,EAAED,EAAEE,SAAS,OAAOD,GAAG,KAAKnB,EAAE,OAAOkB,EAAEA,EAAEG,MAAQ,KAAKd,EAAE,KAAKC,EAAE,KAAKN,EAAE,KAAKE,EAAE,KAAKD,EAAE,KAAKO,EAAE,OAAOQ,EAAE,QAAQ,OAAOA,EAAEA,GAAGA,EAAEE,UAAY,KAAKd,EAAE,KAAKG,EAAE,KAAKI,EAAE,KAAKD,EAAE,KAAKP,EAAE,OAAOa,EAAE,QAAQ,OAAOC,GAAG,KAAKlB,EAAE,OAAOkB,EAAE,CAAC,CAAC,SAASG,EAAEJ,GAAG,OAAOD,EAAEC,KAAKV,CAAC,CACzeb,EAAc4B,OAACN,EAAEtB,EAAiB6B,UAACjB,EAAEZ,EAAA8B,eAAuBjB,EAAEb,EAAA+B,gBAAwBpB,EAAEX,EAAuBgC,gBAACtB,EAAEV,EAAeiC,QAAC5B,EAAEL,EAAkBkC,WAACpB,EAAEd,WAAiBO,EAAEP,EAAAmC,KAAajB,EAAElB,EAAYoC,KAACnB,EAAEjB,EAAcqC,OAAC/B,EAAEN,EAAAsC,SAAiB7B,EAAET,EAAAuC,WAAmB/B,EAAER,EAAAwC,SAAiBzB,EACpRf,EAAAyC,mBAA2B,SAASlB,GAAG,MAAM,iBAAkBA,GAAG,mBAAoBA,GAAGA,IAAIhB,GAAGgB,IAAIV,GAAGU,IAAId,GAAGc,IAAIf,GAAGe,IAAIR,GAAGQ,IAAIP,GAAG,iBAAkBO,GAAG,OAAOA,IAAIA,EAAEE,WAAWP,GAAGK,EAAEE,WAAWR,GAAGM,EAAEE,WAAWf,GAAGa,EAAEE,WAAWd,GAAGY,EAAEE,WAAWX,GAAGS,EAAEE,WAAWN,GAAGI,EAAEE,WAAWL,GAAGG,EAAEE,WAAWJ,EAAE,EAAErB,EAAA0C,YAAoB,SAASnB,GAAG,OAAOI,EAAEJ,IAAID,EAAEC,KAAKX,CAAC,EAAEZ,EAAwB2C,iBAAChB,EAAE3B,EAAA4C,kBAA0B,SAASrB,GAAG,OAAOD,EAAEC,KAAKZ,CAAC,EAAEX,EAAA6C,kBAA0B,SAAStB,GAAG,OAAOD,EAAEC,KAAKb,CAAC,EACleV,EAAA8C,UAAkB,SAASvB,GAAG,MAAM,iBAAkBA,GAAG,OAAOA,GAAGA,EAAEE,WAAWpB,CAAC,EAAEL,eAAqB,SAASuB,GAAG,OAAOD,EAAEC,KAAKT,CAAC,EAAEd,EAAA+C,WAAmB,SAASxB,GAAG,OAAOD,EAAEC,KAAKhB,CAAC,EAAEP,EAAcgD,OAAC,SAASzB,GAAG,OAAOD,EAAEC,KAAKL,CAAC,EAAElB,SAAe,SAASuB,GAAG,OAAOD,EAAEC,KAAKN,CAAC,EAAEjB,EAAAiD,SAAiB,SAAS1B,GAAG,OAAOD,EAAEC,KAAKjB,CAAC,EAAEN,EAAkBkD,WAAC,SAAS3B,GAAG,OAAOD,EAAEC,KAAKd,CAAC,EAAET,eAAqB,SAASuB,GAAG,OAAOD,EAAEC,KAAKf,CAAC,EAAER,EAAAmD,WAAmB,SAAS5B,GAAG,OAAOD,EAAEC,KAAKR,CAAC,+CCXzcqC,EAAApD,QAAiBqD,KCKfC,EAAgB,CAClBC,mBAAmB,EACnBC,aAAa,EACbC,cAAc,EACdC,cAAc,EACdC,aAAa,EACbC,iBAAiB,EACjBC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,QAAQ,EACRC,WAAW,EACXtC,MAAM,GAEJuC,EAAgB,CAClBC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,QAAQ,EACRC,QAAQ,EACRC,WAAW,EACXC,OAAO,GASLC,EAAe,CACjBhD,UAAY,EACZiD,SAAS,EACThB,cAAc,EACdC,aAAa,EACbK,WAAW,EACXtC,MAAM,GAEJiD,EAAe,CAAA,EAGnB,SAASC,EAAWC,GAClB,OAAIC,EAAQC,OAAOF,GACVJ,EAGFE,EAAaE,EAAoB,WAAMvB,CAChD,CARAqB,EAAaG,EAAQ5C,YAhBK,CACxBT,UAAY,EACZuD,QAAQ,EACRtB,cAAc,EACdC,aAAa,EACbK,WAAW,GAqBb,IAAIjE,EAAiBD,OAAOC,eACxBkF,EAAsBnF,OAAOmF,oBAC7BC,EAAwBpF,OAAOoF,sBAC/BC,EAA2BrF,OAAOqF,yBAClCC,EAAiBtF,OAAOsF,eACxBC,EAAkBvF,OAAOsE,UAsC7B,IAAAkB,EArCA,SAASC,EAAqBC,EAAiBC,EAAiBC,GAC9D,GAA+B,iBAApBD,EAA8B,CAEvC,GAAIJ,EAAiB,CACnB,IAAIM,EAAqBP,EAAeK,GAEpCE,GAAsBA,IAAuBN,GAC/CE,EAAqBC,EAAiBG,EAAoBD,EAE7D,CAED,IAAIE,EAAOX,EAAoBQ,GAE3BP,IACFU,EAAOA,EAAKC,OAAOX,EAAsBO,KAM3C,IAHA,IAAIK,EAAgBlB,EAAWY,GAC3BO,EAAgBnB,EAAWa,GAEtBO,EAAI,EAAGA,EAAIJ,EAAKzB,SAAU6B,EAAG,CACpC,IAAIC,EAAML,EAAKI,GAEf,KAAK/B,EAAcgC,IAAUP,GAAaA,EAAUO,IAAWF,GAAiBA,EAAcE,IAAWH,GAAiBA,EAAcG,IAAO,CAC7I,IAAIC,EAAaf,EAAyBM,EAAiBQ,GAE3D,IAEElG,EAAeyF,EAAiBS,EAAKC,EAC/C,CAAU,MAAO3F,GAAK,CACf,CACF,CACF,CAED,OAAOiF,CACT,SCzFe,SAIbA,EACAC,GALa,OAMVF,EAAqBC,EAAiBC,EAN3C"}