import sqlite3 from 'sqlite3';
export declare class Database {
    private static instance;
    private db;
    private constructor();
    static getInstance(): Database;
    private initializeTables;
    run(sql: string, params?: any[]): Promise<sqlite3.RunResult>;
    get(sql: string, params?: any[]): Promise<any>;
    all(sql: string, params?: any[]): Promise<any[]>;
    close(): Promise<void>;
}
//# sourceMappingURL=database.d.ts.map