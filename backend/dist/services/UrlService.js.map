{"version": 3, "file": "UrlService.js", "sourceRoot": "", "sources": ["../../src/services/UrlService.ts"], "names": [], "mappings": ";;;AAAA,iDAA8C;AAE9C,oEAAiE;AACjE,wDAAqD;AACrD,yDAAmD;AAEnD,MAAa,UAAU;IAGrB;QACE,IAAI,CAAC,EAAE,GAAG,mBAAQ,CAAC,WAAW,EAAE,CAAC;IACnC,CAAC;IAKM,KAAK,CAAC,cAAc,CAAC,OAAyB;QACnD,IAAI,CAAC;YACH,IAAA,YAAG,EAAC,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,2BAA2B,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;YAG5E,MAAM,aAAa,GAAG,2BAAY,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAC5D,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;gBAC3B,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YACvC,CAAC;YAGD,MAAM,kBAAkB,GAAG,2BAAY,CAAC,uBAAuB,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;YACzF,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;gBAChC,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAC5C,CAAC;YAGD,MAAM,SAAS,GAAG,MAAM,uCAAkB,CAAC,kBAAkB,CAC3D,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,EACxC,OAAO,CAAC,UAAU,CACnB,CAAC;YAGF,MAAM,UAAU,GAAG,2BAAY,CAAC,mBAAmB,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;YAGrF,MAAM,GAAG,GAAQ;gBACf,YAAY,EAAE,aAAa,CAAC,aAAc;gBAC1C,UAAU,EAAE,SAAS;gBACrB,UAAU,EAAE,UAAU,CAAC,WAAW,EAAE;gBACpC,SAAS,EAAE,IAAI;gBACf,YAAY,EAAE,CAAC;aAChB,CAAC;YAEF,MAAM,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAExB,IAAA,YAAG,EAAC,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,mCAAmC,SAAS,EAAE,CAAC,CAAC;YAElF,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,QAAQ,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,uBAAuB,IAAI,SAAS,EAAE;gBAC3E,WAAW,EAAE,GAAG,CAAC,YAAY;gBAC7B,SAAS,EAAE,SAAS;gBACpB,SAAS,EAAE,UAAU,CAAC,WAAW,EAAE;aACpC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAA,YAAG,EAAC,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,+BAA+B,KAAK,EAAE,CAAC,CAAC;YAC3E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKM,KAAK,CAAC,cAAc,CAAC,SAAiB,EAAE,UAAgD;QAC7F,IAAI,CAAC;YACH,IAAA,YAAG,EAAC,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,gCAAgC,SAAS,EAAE,CAAC,CAAC;YAE/E,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YACpD,IAAI,CAAC,GAAG,EAAE,CAAC;gBACT,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;YACzC,CAAC;YAED,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC;gBACnB,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;YAC3C,CAAC;YAED,IAAI,2BAAY,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;gBAE3C,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;gBACpC,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;YAC3C,CAAC;YAGD,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;YAG/C,MAAM,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;YAEvC,IAAA,YAAG,EAAC,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,8BAA8B,SAAS,OAAO,GAAG,CAAC,YAAY,EAAE,CAAC,CAAC;YACpG,OAAO,GAAG,CAAC,YAAY,CAAC;QAC1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAA,YAAG,EAAC,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,8BAA8B,SAAS,KAAK,KAAK,EAAE,CAAC,CAAC;YACxF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKM,KAAK,CAAC,WAAW,CAAC,SAAiB;QACxC,IAAI,CAAC;YACH,IAAA,YAAG,EAAC,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,yBAAyB,SAAS,EAAE,CAAC,CAAC;YAExE,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YACpD,IAAI,CAAC,GAAG,EAAE,CAAC;gBACT,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;YACzC,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,GAAG,CAAC,UAAU;gBACzB,WAAW,EAAE,GAAG,CAAC,YAAY;gBAC7B,SAAS,EAAE,GAAG,CAAC,UAAW;gBAC1B,SAAS,EAAE,GAAG,CAAC,UAAU;gBACzB,WAAW,EAAE,GAAG,CAAC,YAAY,IAAI,CAAC;gBAClC,YAAY,EAAE,GAAG,CAAC,aAAa,IAAI,IAAI;gBACvC,QAAQ,EAAE,GAAG,CAAC,SAAS,IAAI,KAAK;aACjC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAA,YAAG,EAAC,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,gCAAgC,SAAS,KAAK,KAAK,EAAE,CAAC,CAAC;YAC1F,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,wBAAwB,CAAC,SAAiB;QACtD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,GAAG,CAChC,kDAAkD,EAClD,CAAC,SAAS,CAAC,CACZ,CAAC;YACF,OAAO,CAAC,QAAQ,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAA,YAAG,EAAC,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,iCAAiC,SAAS,KAAK,KAAK,EAAE,CAAC,CAAC;YAC3F,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,OAAO,CAAC,GAAQ;QAC5B,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,EAAE,CAAC,GAAG,CACf;gCACwB,EACxB,CAAC,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC,YAAY,CAAC,CACpF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAA,YAAG,EAAC,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,uBAAuB,KAAK,EAAE,CAAC,CAAC;YACnE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,iBAAiB,CAAC,SAAiB;QAC/C,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,EAAE,CAAC,GAAG,CACtB,yCAAyC,EACzC,CAAC,SAAS,CAAC,CACZ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAA,YAAG,EAAC,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,oCAAoC,KAAK,EAAE,CAAC,CAAC;YAChF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,YAAY,CAAC,SAAiB,EAAE,UAAgD;QAC5F,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,EAAE,CAAC,GAAG,CACf,6EAA6E,EAC7E,CAAC,SAAS,EAAE,UAAU,EAAE,EAAE,IAAI,IAAI,EAAE,UAAU,EAAE,SAAS,IAAI,IAAI,CAAC,CACnE,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAA,YAAG,EAAC,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,+BAA+B,KAAK,EAAE,CAAC,CAAC;QAE5E,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,gBAAgB,CAAC,SAAiB;QAC9C,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,EAAE,CAAC,GAAG,CACf;8BACsB,EACtB,CAAC,SAAS,CAAC,CACZ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAA,YAAG,EAAC,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,iCAAiC,KAAK,EAAE,CAAC,CAAC;YAC7E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,aAAa,CAAC,SAAiB;QAC3C,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,EAAE,CAAC,GAAG,CACf,oDAAoD,EACpD,CAAC,SAAS,CAAC,CACZ,CAAC;YACF,IAAA,YAAG,EAAC,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,oBAAoB,SAAS,EAAE,CAAC,CAAC;QACrE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAA,YAAG,EAAC,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,6BAA6B,KAAK,EAAE,CAAC,CAAC;QAE3E,CAAC;IACH,CAAC;CACF;AA1ND,gCA0NC"}