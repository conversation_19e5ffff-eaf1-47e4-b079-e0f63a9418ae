"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UrlService = void 0;
const database_1 = require("../config/database");
const shortCodeGenerator_1 = require("../utils/shortCodeGenerator");
const urlValidator_1 = require("../utils/urlValidator");
const logger_1 = require("../logging_middleware/logger");
class UrlService {
    constructor() {
        this.db = database_1.Database.getInstance();
    }
    async createShortUrl(request) {
        try {
            (0, logger_1.Log)('backend', 'info', 'service', `Creating short URL for: ${request.url}`);
            const urlValidation = urlValidator_1.UrlValidator.validateUrl(request.url);
            if (!urlValidation.isValid) {
                throw new Error(urlValidation.error);
            }
            const validityValidation = urlValidator_1.UrlValidator.validateValidityMinutes(request.validityMinutes);
            if (!validityValidation.isValid) {
                throw new Error(validityValidation.error);
            }
            const shortCode = await shortCodeGenerator_1.ShortCodeGenerator.generateUniqueCode(this.checkShortCodeUniqueness.bind(this), request.customCode);
            const expiryDate = urlValidator_1.UrlValidator.calculateExpiryDate(validityValidation.validMinutes);
            const url = {
                original_url: urlValidation.normalizedUrl,
                short_code: shortCode,
                expires_at: expiryDate.toISOString(),
                is_active: true,
                access_count: 0
            };
            await this.saveUrl(url);
            (0, logger_1.Log)('backend', 'info', 'service', `Short URL created successfully: ${shortCode}`);
            return {
                success: true,
                shortUrl: `${process.env.BASE_URL || 'http://localhost:3000'}/${shortCode}`,
                originalUrl: url.original_url,
                shortCode: shortCode,
                expiresAt: expiryDate.toISOString()
            };
        }
        catch (error) {
            (0, logger_1.Log)('backend', 'error', 'service', `Failed to create short URL: ${error}`);
            throw error;
        }
    }
    async getOriginalUrl(shortCode, clientInfo) {
        try {
            (0, logger_1.Log)('backend', 'info', 'service', `Retrieving original URL for: ${shortCode}`);
            const url = await this.getUrlByShortCode(shortCode);
            if (!url) {
                throw new Error('Short URL not found');
            }
            if (!url.is_active) {
                throw new Error('Short URL is inactive');
            }
            if (urlValidator_1.UrlValidator.isExpired(url.expires_at)) {
                await this.deactivateUrl(shortCode);
                throw new Error('Short URL has expired');
            }
            await this.recordAccess(shortCode, clientInfo);
            await this.updateAccessInfo(shortCode);
            (0, logger_1.Log)('backend', 'info', 'service', `URL accessed successfully: ${shortCode} -> ${url.original_url}`);
            return url.original_url;
        }
        catch (error) {
            (0, logger_1.Log)('backend', 'error', 'service', `Failed to retrieve URL for ${shortCode}: ${error}`);
            throw error;
        }
    }
    async getUrlStats(shortCode) {
        try {
            (0, logger_1.Log)('backend', 'info', 'service', `Retrieving stats for: ${shortCode}`);
            const url = await this.getUrlByShortCode(shortCode);
            if (!url) {
                throw new Error('Short URL not found');
            }
            return {
                success: true,
                shortCode: url.short_code,
                originalUrl: url.original_url,
                createdAt: url.created_at,
                expiresAt: url.expires_at,
                accessCount: url.access_count || 0,
                lastAccessed: url.last_accessed || null,
                isActive: url.is_active || false
            };
        }
        catch (error) {
            (0, logger_1.Log)('backend', 'error', 'service', `Failed to retrieve stats for ${shortCode}: ${error}`);
            throw error;
        }
    }
    async checkShortCodeUniqueness(shortCode) {
        try {
            const existing = await this.db.get('SELECT short_code FROM urls WHERE short_code = ?', [shortCode]);
            return !existing;
        }
        catch (error) {
            (0, logger_1.Log)('backend', 'error', 'service', `Error checking uniqueness for ${shortCode}: ${error}`);
            throw error;
        }
    }
    async saveUrl(url) {
        try {
            await this.db.run(`INSERT INTO urls (original_url, short_code, expires_at, is_active, access_count) 
         VALUES (?, ?, ?, ?, ?)`, [url.original_url, url.short_code, url.expires_at, url.is_active, url.access_count]);
        }
        catch (error) {
            (0, logger_1.Log)('backend', 'error', 'service', `Failed to save URL: ${error}`);
            throw error;
        }
    }
    async getUrlByShortCode(shortCode) {
        try {
            return await this.db.get('SELECT * FROM urls WHERE short_code = ?', [shortCode]);
        }
        catch (error) {
            (0, logger_1.Log)('backend', 'error', 'service', `Failed to get URL by short code: ${error}`);
            throw error;
        }
    }
    async recordAccess(shortCode, clientInfo) {
        try {
            await this.db.run(`INSERT INTO analytics (short_code, ip_address, user_agent) VALUES (?, ?, ?)`, [shortCode, clientInfo?.ip || null, clientInfo?.userAgent || null]);
        }
        catch (error) {
            (0, logger_1.Log)('backend', 'warn', 'service', `Failed to record analytics: ${error}`);
        }
    }
    async updateAccessInfo(shortCode) {
        try {
            await this.db.run(`UPDATE urls SET access_count = access_count + 1, last_accessed = CURRENT_TIMESTAMP 
         WHERE short_code = ?`, [shortCode]);
        }
        catch (error) {
            (0, logger_1.Log)('backend', 'error', 'service', `Failed to update access info: ${error}`);
            throw error;
        }
    }
    async deactivateUrl(shortCode) {
        try {
            await this.db.run('UPDATE urls SET is_active = 0 WHERE short_code = ?', [shortCode]);
            (0, logger_1.Log)('backend', 'info', 'service', `URL deactivated: ${shortCode}`);
        }
        catch (error) {
            (0, logger_1.Log)('backend', 'error', 'service', `Failed to deactivate URL: ${error}`);
        }
    }
}
exports.UrlService = UrlService;
//# sourceMappingURL=UrlService.js.map