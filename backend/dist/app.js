"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.App = void 0;
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const urlRoutes_1 = __importDefault(require("./routes/urlRoutes"));
const requestLogger_1 = require("./middleware/requestLogger");
const validation_1 = require("./middleware/validation");
const database_1 = require("./config/database");
const logger_1 = require("../../logging_middleware/logger");
class App {
    constructor() {
        this.app = (0, express_1.default)();
        this.db = database_1.Database.getInstance();
        this.initializeMiddlewares();
        this.initializeRoutes();
        this.initializeErrorHandling();
        this.logAppInitialization();
    }
    initializeMiddlewares() {
        this.app.use((0, helmet_1.default)({
            contentSecurityPolicy: false,
            crossOriginEmbedderPolicy: false
        }));
        this.app.use((0, cors_1.default)({
            origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000', 'http://localhost:3001'],
            credentials: true,
            methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
            allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
        }));
        this.app.use(express_1.default.json({ limit: '10mb' }));
        this.app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
        this.app.set('trust proxy', 1);
        this.app.use(requestLogger_1.securityHeaders);
        this.app.use(requestLogger_1.requestLogger);
        (0, logger_1.Log)('backend', 'info', 'config', 'Middlewares initialized successfully');
    }
    initializeRoutes() {
        this.app.get('/health', (req, res) => {
            res.status(200).json({
                success: true,
                message: 'URL Shortener service is healthy',
                timestamp: new Date().toISOString(),
                version: '1.0.0'
            });
        });
        this.app.use('/', urlRoutes_1.default);
        (0, logger_1.Log)('backend', 'info', 'config', 'Routes initialized successfully');
    }
    initializeErrorHandling() {
        this.app.use(validation_1.notFoundHandler);
        this.app.use(validation_1.errorHandler);
        (0, logger_1.Log)('backend', 'info', 'config', 'Error handling initialized successfully');
    }
    logAppInitialization() {
        (0, logger_1.Log)('backend', 'info', 'config', 'URL Shortener application initialized successfully');
        (0, logger_1.Log)('backend', 'info', 'config', `Environment: ${process.env.NODE_ENV || 'development'}`);
        (0, logger_1.Log)('backend', 'info', 'config', `Base URL: ${process.env.BASE_URL || 'http://localhost:3000'}`);
    }
    listen(port) {
        this.app.listen(port, () => {
            (0, logger_1.Log)('backend', 'info', 'config', `Server is running on port ${port}`);
            (0, logger_1.Log)('backend', 'info', 'config', `API endpoints available at http://localhost:${port}/api`);
            console.log(`🚀 URL Shortener service is running on port ${port}`);
            console.log(`📊 Health check: http://localhost:${port}/health`);
            console.log(`📝 API Documentation: http://localhost:${port}/api/health`);
        });
    }
    getApp() {
        return this.app;
    }
    async shutdown() {
        try {
            await this.db.close();
            (0, logger_1.Log)('backend', 'info', 'config', 'Application shutdown completed');
        }
        catch (error) {
            (0, logger_1.Log)('backend', 'error', 'config', `Error during shutdown: ${error}`);
        }
    }
}
exports.App = App;
//# sourceMappingURL=app.js.map