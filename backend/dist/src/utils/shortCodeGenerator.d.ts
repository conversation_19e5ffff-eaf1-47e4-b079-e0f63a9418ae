export declare class ShortCodeGenerator {
    private static readonly ALPHABET;
    private static readonly DEFAULT_LENGTH;
    private static readonly MAX_LENGTH;
    private static readonly MIN_LENGTH;
    static generateRandomCode(length?: number): string;
    static validateCustomCode(code: string): {
        isValid: boolean;
        error?: string;
    };
    static generateUniqueCode(checkUniqueness: (code: string) => Promise<boolean>, customCode?: string, maxRetries?: number): Promise<string>;
}
//# sourceMappingURL=shortCodeGenerator.d.ts.map