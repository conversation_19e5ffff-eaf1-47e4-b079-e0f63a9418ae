{"version": 3, "file": "urlValidator.js", "sourceRoot": "", "sources": ["../../../src/utils/urlValidator.ts"], "names": [], "mappings": ";;;AAAA,+DAAyD;AAEzD,MAAa,YAAY;IAOhB,MAAM,CAAC,WAAW,CAAC,GAAW;QACnC,IAAI,CAAC;YACH,IAAI,CAAC,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;gBACpC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,sCAAsC,EAAE,CAAC;YAC3E,CAAC;YAED,IAAI,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;gBACrC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,qBAAqB,IAAI,CAAC,cAAc,aAAa,EAAE,CAAC;YAC1F,CAAC;YAGD,IAAI,aAAa,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;YAC/B,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE,CAAC;gBACzC,aAAa,GAAG,UAAU,GAAG,aAAa,CAAC;YAC7C,CAAC;YAGD,IAAI,SAAc,CAAC;YACnB,IAAI,CAAC;gBACH,SAAS,GAAG,IAAI,GAAG,CAAC,aAAa,CAAC,CAAC;YACrC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,oBAAoB,EAAE,CAAC;YACzD,CAAC;YAGD,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACzD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,2CAA2C,EAAE,CAAC;YAChF,CAAC;YAGD,MAAM,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;YAClD,IAAI,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACxC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,gEAAgE,EAAE,CAAC;YACrG,CAAC;YAED,IAAA,YAAG,EAAC,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,+BAA+B,aAAa,EAAE,CAAC,CAAC;YACjF,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC;QAC1C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAA,YAAG,EAAC,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,yBAAyB,KAAK,EAAE,CAAC,CAAC;YACnE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC;QAC5D,CAAC;IACH,CAAC;IAKO,MAAM,CAAC,oBAAoB,CAAC,QAAgB;QAElD,IAAI,QAAQ,KAAK,WAAW,IAAI,QAAQ,KAAK,WAAW,IAAI,QAAQ,KAAK,KAAK,EAAE,CAAC;YAC/E,OAAO,IAAI,CAAC;QACd,CAAC;QAGD,MAAM,aAAa,GAAG;YACpB,OAAO;YACP,gCAAgC;YAChC,aAAa;YACb,aAAa;SACd,CAAC;QAEF,OAAO,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC3D,CAAC;IAKM,MAAM,CAAC,uBAAuB,CAAC,OAAgB;QACpD,MAAM,gBAAgB,GAAG,EAAE,CAAC;QAC5B,MAAM,YAAY,GAAG,MAAM,CAAC;QAC5B,MAAM,YAAY,GAAG,CAAC,CAAC;QAEvB,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,KAAK,IAAI,EAAE,CAAC;YAC9C,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,gBAAgB,EAAE,CAAC;QAC3D,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,OAAO,GAAG,YAAY,EAAE,CAAC;YACzD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,6BAA6B,YAAY,SAAS,EAAE,YAAY,EAAE,gBAAgB,EAAE,CAAC;QACvH,CAAC;QAED,IAAI,OAAO,GAAG,YAAY,EAAE,CAAC;YAC3B,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,0BAA0B,YAAY,mBAAmB,EAAE,YAAY,EAAE,gBAAgB,EAAE,CAAC;QAC9H,CAAC;QAED,IAAA,YAAG,EAAC,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,8BAA8B,OAAO,UAAU,CAAC,CAAC;QAClF,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,CAAC;IAClD,CAAC;IAKM,MAAM,CAAC,mBAAmB,CAAC,eAAuB;QACvD,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,eAAe,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QACzE,IAAA,YAAG,EAAC,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,2BAA2B,UAAU,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;QACxF,OAAO,UAAU,CAAC;IACpB,CAAC;IAKM,MAAM,CAAC,SAAS,CAAC,UAAyB;QAC/C,MAAM,MAAM,GAAG,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;QAClF,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,OAAO,GAAG,GAAG,GAAG,MAAM,CAAC;QAE7B,IAAI,OAAO,EAAE,CAAC;YACZ,IAAA,YAAG,EAAC,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,oBAAoB,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;QAC/E,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;;AArHH,oCAsHC;AArHyB,2BAAc,GAAG,IAAI,CAAC;AACtB,8BAAiB,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC"}