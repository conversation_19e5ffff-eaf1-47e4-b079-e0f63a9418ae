"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UrlValidator = void 0;
const logger_1 = require("../../../logging_middleware/logger");
class UrlValidator {
    static validateUrl(url) {
        try {
            if (!url || typeof url !== 'string') {
                return { isValid: false, error: 'URL is required and must be a string' };
            }
            if (url.length > this.MAX_URL_LENGTH) {
                return { isValid: false, error: `URL cannot exceed ${this.MAX_URL_LENGTH} characters` };
            }
            let normalizedUrl = url.trim();
            if (!normalizedUrl.match(/^https?:\/\//)) {
                normalizedUrl = 'https://' + normalizedUrl;
            }
            let parsedUrl;
            try {
                parsedUrl = new URL(normalizedUrl);
            }
            catch (error) {
                return { isValid: false, error: 'Invalid URL format' };
            }
            if (!this.ALLOWED_PROTOCOLS.includes(parsedUrl.protocol)) {
                return { isValid: false, error: 'Only HTTP and HTTPS protocols are allowed' };
            }
            const hostname = parsedUrl.hostname.toLowerCase();
            if (this.isPrivateOrLocalhost(hostname)) {
                return { isValid: false, error: 'URLs pointing to localhost or private networks are not allowed' };
            }
            (0, logger_1.Log)('backend', 'debug', 'utils', `URL validated successfully: ${normalizedUrl}`);
            return { isValid: true, normalizedUrl };
        }
        catch (error) {
            (0, logger_1.Log)('backend', 'error', 'utils', `URL validation error: ${error}`);
            return { isValid: false, error: 'URL validation failed' };
        }
    }
    static isPrivateOrLocalhost(hostname) {
        if (hostname === 'localhost' || hostname === '127.0.0.1' || hostname === '::1') {
            return true;
        }
        const privateRanges = [
            /^10\./,
            /^172\.(1[6-9]|2[0-9]|3[0-1])\./,
            /^192\.168\./,
            /^169\.254\./,
        ];
        return privateRanges.some(range => range.test(hostname));
    }
    static validateValidityMinutes(minutes) {
        const DEFAULT_VALIDITY = 30;
        const MAX_VALIDITY = 525600;
        const MIN_VALIDITY = 1;
        if (minutes === undefined || minutes === null) {
            return { isValid: true, validMinutes: DEFAULT_VALIDITY };
        }
        if (!Number.isInteger(minutes) || minutes < MIN_VALIDITY) {
            return { isValid: false, error: `Validity must be at least ${MIN_VALIDITY} minute`, validMinutes: DEFAULT_VALIDITY };
        }
        if (minutes > MAX_VALIDITY) {
            return { isValid: false, error: `Validity cannot exceed ${MAX_VALIDITY} minutes (1 year)`, validMinutes: DEFAULT_VALIDITY };
        }
        (0, logger_1.Log)('backend', 'debug', 'utils', `Validity period validated: ${minutes} minutes`);
        return { isValid: true, validMinutes: minutes };
    }
    static calculateExpiryDate(validityMinutes) {
        const now = new Date();
        const expiryDate = new Date(now.getTime() + validityMinutes * 60 * 1000);
        (0, logger_1.Log)('backend', 'debug', 'utils', `Calculated expiry date: ${expiryDate.toISOString()}`);
        return expiryDate;
    }
    static isExpired(expiryDate) {
        const expiry = typeof expiryDate === 'string' ? new Date(expiryDate) : expiryDate;
        const now = new Date();
        const expired = now > expiry;
        if (expired) {
            (0, logger_1.Log)('backend', 'debug', 'utils', `URL has expired: ${expiry.toISOString()}`);
        }
        return expired;
    }
}
exports.UrlValidator = UrlValidator;
UrlValidator.MAX_URL_LENGTH = 2048;
UrlValidator.ALLOWED_PROTOCOLS = ['http:', 'https:'];
//# sourceMappingURL=urlValidator.js.map