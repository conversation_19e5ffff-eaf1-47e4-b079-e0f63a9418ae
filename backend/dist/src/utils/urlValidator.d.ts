export declare class UrlValidator {
    private static readonly MAX_URL_LENGTH;
    private static readonly ALLOWED_PROTOCOLS;
    static validateUrl(url: string): {
        isValid: boolean;
        error?: string;
        normalizedUrl?: string;
    };
    private static isPrivateOrLocalhost;
    static validateValidityMinutes(minutes?: number): {
        isValid: boolean;
        error?: string;
        validMinutes: number;
    };
    static calculateExpiryDate(validityMinutes: number): Date;
    static isExpired(expiryDate: string | Date): boolean;
}
//# sourceMappingURL=urlValidator.d.ts.map