import { CreateUrlRequest, CreateUrlResponse, UrlStatsResponse } from '../models/Url';
export declare class UrlService {
    private db;
    constructor();
    createShortUrl(request: CreateUrlRequest): Promise<CreateUrlResponse>;
    getOriginalUrl(shortCode: string, clientInfo?: {
        ip?: string;
        userAgent?: string;
    }): Promise<string>;
    getUrlStats(shortCode: string): Promise<UrlStatsResponse>;
    private checkShortCodeUniqueness;
    private saveUrl;
    private getUrlByShortCode;
    private recordAccess;
    private updateAccessInfo;
    private deactivateUrl;
}
//# sourceMappingURL=UrlService.d.ts.map