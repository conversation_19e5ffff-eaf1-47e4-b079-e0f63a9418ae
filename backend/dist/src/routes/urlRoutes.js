"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const UrlController_1 = require("../controllers/UrlController");
const validation_1 = require("../middleware/validation");
const rateLimiter_1 = require("../middleware/rateLimiter");
const router = (0, express_1.Router)();
const urlController = new UrlController_1.UrlController();
router.use(rateLimiter_1.rateLimiter);
router.post('/api/shorten', rateLimiter_1.createUrlRateLimiter, validation_1.validateCreateUrlRequest, urlController.createShortUrl);
router.get('/api/stats/:shortCode', urlController.getUrlStats);
router.get('/api/health', urlController.healthCheck);
router.get('/:shortCode', urlController.redirectToOriginalUrl);
exports.default = router;
//# sourceMappingURL=urlRoutes.js.map