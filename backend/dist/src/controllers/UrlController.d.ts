import { Request, Response } from 'express';
export declare class UrlController {
    private urlService;
    constructor();
    createShortUrl: (req: Request, res: Response) => Promise<void>;
    redirectToOriginalUrl: (req: Request, res: Response) => Promise<void>;
    getUrlStats: (req: Request, res: Response) => Promise<void>;
    healthCheck: (req: Request, res: Response) => Promise<void>;
}
//# sourceMappingURL=UrlController.d.ts.map