"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.notFoundHandler = exports.errorHandler = exports.validateCreateUrlRequest = void 0;
const express_validator_1 = require("express-validator");
const logger_1 = require("../../../logging_middleware/logger");
exports.validateCreateUrlRequest = [
    (0, express_validator_1.body)('url')
        .notEmpty()
        .withMessage('URL is required')
        .isLength({ max: 2048 })
        .withMessage('URL cannot exceed 2048 characters')
        .custom((value) => {
        try {
            const url = value.startsWith('http') ? value : `https://${value}`;
            new URL(url);
            return true;
        }
        catch {
            throw new Error('Invalid URL format');
        }
    }),
    (0, express_validator_1.body)('customCode')
        .optional()
        .isLength({ min: 3, max: 20 })
        .withMessage('Custom code must be between 3 and 20 characters')
        .matches(/^[a-zA-Z0-9]+$/)
        .withMessage('Custom code can only contain alphanumeric characters')
        .custom((value) => {
        const reservedWords = ['api', 'admin', 'www', 'app', 'stats', 'analytics', 'health'];
        if (reservedWords.includes(value.toLowerCase())) {
            throw new Error('Custom code cannot be a reserved word');
        }
        return true;
    }),
    (0, express_validator_1.body)('validityMinutes')
        .optional()
        .isInt({ min: 1, max: 525600 })
        .withMessage('Validity must be between 1 and 525600 minutes (1 year)'),
    (req, res, next) => {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            const errorMessages = errors.array().map(error => error.msg).join(', ');
            (0, logger_1.Log)('backend', 'warn', 'middleware', `Validation failed: ${errorMessages}`);
            const errorResponse = {
                success: false,
                error: 'VALIDATION_ERROR',
                message: errorMessages
            };
            res.status(400).json(errorResponse);
            return;
        }
        next();
    }
];
const errorHandler = (error, req, res, next) => {
    (0, logger_1.Log)('backend', 'error', 'middleware', `Unhandled error: ${error.message}`);
    (0, logger_1.Log)('backend', 'error', 'middleware', `Stack trace: ${error.stack}`);
    const errorResponse = {
        success: false,
        error: 'INTERNAL_SERVER_ERROR',
        message: 'An unexpected error occurred'
    };
    res.status(500).json(errorResponse);
};
exports.errorHandler = errorHandler;
const notFoundHandler = (req, res) => {
    (0, logger_1.Log)('backend', 'warn', 'middleware', `404 - Route not found: ${req.method} ${req.path}`);
    const errorResponse = {
        success: false,
        error: 'NOT_FOUND',
        message: 'Route not found'
    };
    res.status(404).json(errorResponse);
};
exports.notFoundHandler = notFoundHandler;
//# sourceMappingURL=validation.js.map