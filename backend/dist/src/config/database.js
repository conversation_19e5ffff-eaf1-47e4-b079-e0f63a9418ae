"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Database = void 0;
const sqlite3_1 = __importDefault(require("sqlite3"));
const path_1 = __importDefault(require("path"));
const logger_1 = require("../../../logging_middleware/logger");
class Database {
    constructor() {
        const dbPath = path_1.default.join(__dirname, '../../data/urls.db');
        this.db = new sqlite3_1.default.Database(dbPath, (err) => {
            if (err) {
                (0, logger_1.Log)('backend', 'error', 'db', `Failed to connect to database: ${err.message}`);
                throw err;
            }
            (0, logger_1.Log)('backend', 'info', 'db', 'Connected to SQLite database');
        });
        this.initializeTables();
    }
    static getInstance() {
        if (!Database.instance) {
            Database.instance = new Database();
        }
        return Database.instance;
    }
    async initializeTables() {
        const createUrlsTable = `
      CREATE TABLE IF NOT EXISTS urls (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        original_url TEXT NOT NULL,
        short_code TEXT UNIQUE NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        expires_at DATETIME NOT NULL,
        is_active BOOLEAN DEFAULT 1,
        access_count INTEGER DEFAULT 0,
        last_accessed DATETIME NULL
      )
    `;
        const createAnalyticsTable = `
      CREATE TABLE IF NOT EXISTS analytics (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        short_code TEXT NOT NULL,
        accessed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        ip_address TEXT,
        user_agent TEXT,
        FOREIGN KEY (short_code) REFERENCES urls (short_code)
      )
    `;
        try {
            await this.run(createUrlsTable);
            await this.run(createAnalyticsTable);
            (0, logger_1.Log)('backend', 'info', 'db', 'Database tables initialized successfully');
        }
        catch (error) {
            (0, logger_1.Log)('backend', 'error', 'db', `Failed to initialize tables: ${error}`);
            throw error;
        }
    }
    run(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.run(sql, params, function (err) {
                if (err) {
                    (0, logger_1.Log)('backend', 'error', 'db', `SQL execution failed: ${err.message}`);
                    reject(err);
                }
                else {
                    resolve(this);
                }
            });
        });
    }
    get(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.get(sql, params, (err, row) => {
                if (err) {
                    (0, logger_1.Log)('backend', 'error', 'db', `SQL query failed: ${err.message}`);
                    reject(err);
                }
                else {
                    resolve(row);
                }
            });
        });
    }
    all(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    (0, logger_1.Log)('backend', 'error', 'db', `SQL query failed: ${err.message}`);
                    reject(err);
                }
                else {
                    resolve(rows);
                }
            });
        });
    }
    close() {
        return new Promise((resolve, reject) => {
            this.db.close((err) => {
                if (err) {
                    (0, logger_1.Log)('backend', 'error', 'db', `Failed to close database: ${err.message}`);
                    reject(err);
                }
                else {
                    (0, logger_1.Log)('backend', 'info', 'db', 'Database connection closed');
                    resolve();
                }
            });
        });
    }
}
exports.Database = Database;
//# sourceMappingURL=database.js.map