"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const app_1 = require("./app");
const logger_1 = require("./logging_middleware/logger");
const PORT = parseInt(process.env.PORT || '3000', 10);
const NODE_ENV = process.env.NODE_ENV || 'development';
if (!process.env.BASE_URL) {
    process.env.BASE_URL = `http://localhost:${PORT}`;
}
async function startServer() {
    try {
        (0, logger_1.Log)('backend', 'info', 'config', 'Starting URL Shortener service...');
        (0, logger_1.Log)('backend', 'info', 'config', `Environment: ${NODE_ENV}`);
        (0, logger_1.Log)('backend', 'info', 'config', `Port: ${PORT}`);
        (0, logger_1.Log)('backend', 'info', 'config', `Base URL: ${process.env.BASE_URL}`);
        const app = new app_1.App();
        app.listen(PORT);
        const gracefulShutdown = async (signal) => {
            (0, logger_1.Log)('backend', 'info', 'config', `Received ${signal}. Starting graceful shutdown...`);
            try {
                await app.shutdown();
                (0, logger_1.Log)('backend', 'info', 'config', 'Graceful shutdown completed');
                process.exit(0);
            }
            catch (error) {
                (0, logger_1.Log)('backend', 'error', 'config', `Error during graceful shutdown: ${error}`);
                process.exit(1);
            }
        };
        process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
        process.on('SIGINT', () => gracefulShutdown('SIGINT'));
        process.on('uncaughtException', (error) => {
            (0, logger_1.Log)('backend', 'fatal', 'config', `Uncaught Exception: ${error.message}`);
            (0, logger_1.Log)('backend', 'fatal', 'config', `Stack: ${error.stack}`);
            process.exit(1);
        });
        process.on('unhandledRejection', (reason, promise) => {
            (0, logger_1.Log)('backend', 'fatal', 'config', `Unhandled Rejection at: ${promise}, reason: ${reason}`);
            process.exit(1);
        });
    }
    catch (error) {
        (0, logger_1.Log)('backend', 'fatal', 'config', `Failed to start server: ${error}`);
        console.error('Failed to start server:', error);
        process.exit(1);
    }
}
startServer();
//# sourceMappingURL=index.js.map