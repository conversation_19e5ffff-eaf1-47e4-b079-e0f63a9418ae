export interface Url {
    id?: number;
    original_url: string;
    short_code: string;
    created_at?: string;
    expires_at: string;
    is_active?: boolean;
    access_count?: number;
    last_accessed?: string | null;
}
export interface UrlAnalytics {
    id?: number;
    short_code: string;
    accessed_at?: string;
    ip_address?: string;
    user_agent?: string;
}
export interface CreateUrlRequest {
    url: string;
    customCode?: string;
    validityMinutes?: number;
}
export interface CreateUrlResponse {
    success: boolean;
    shortUrl: string;
    originalUrl: string;
    shortCode: string;
    expiresAt: string;
    message?: string;
}
export interface UrlStatsResponse {
    success: boolean;
    shortCode: string;
    originalUrl: string;
    createdAt: string;
    expiresAt: string;
    accessCount: number;
    lastAccessed: string | null;
    isActive: boolean;
}
export interface ErrorResponse {
    success: false;
    error: string;
    message: string;
}
//# sourceMappingURL=Url.d.ts.map