"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createUrlRateLimiter = exports.rateLimiter = void 0;
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const logger_1 = require("../logging_middleware/logger");
exports.rateLimiter = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000,
    max: 100,
    message: {
        success: false,
        error: 'RATE_LIMIT_EXCEEDED',
        message: 'Too many requests from this IP, please try again later.'
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res) => {
        (0, logger_1.Log)('backend', 'warn', 'middleware', `Rate limit exceeded for IP: ${req.ip}`);
        const errorResponse = {
            success: false,
            error: 'RATE_LIMIT_EXCEEDED',
            message: 'Too many requests from this IP, please try again later.'
        };
        res.status(429).json(errorResponse);
    },
    skip: (req) => {
        return req.path === '/api/health';
    }
});
exports.createUrlRateLimiter = (0, express_rate_limit_1.default)({
    windowMs: 5 * 60 * 1000,
    max: 10,
    message: {
        success: false,
        error: 'CREATE_RATE_LIMIT_EXCEEDED',
        message: 'Too many URL creation requests from this IP, please try again later.'
    },
    handler: (req, res) => {
        (0, logger_1.Log)('backend', 'warn', 'middleware', `URL creation rate limit exceeded for IP: ${req.ip}`);
        const errorResponse = {
            success: false,
            error: 'CREATE_RATE_LIMIT_EXCEEDED',
            message: 'Too many URL creation requests from this IP, please try again later.'
        };
        res.status(429).json(errorResponse);
    }
});
//# sourceMappingURL=rateLimiter.js.map