"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.securityHeaders = exports.requestLogger = void 0;
const logger_1 = require("../logging_middleware/logger");
const requestLogger = (req, res, next) => {
    const startTime = Date.now();
    const { method, url, ip } = req;
    const userAgent = req.get('User-Agent') || 'Unknown';
    (0, logger_1.Log)('backend', 'info', 'middleware', `${method} ${url} - IP: ${ip} - User-Agent: ${userAgent}`);
    res.on('finish', () => {
        const duration = Date.now() - startTime;
        const { statusCode } = res;
        let logLevel = 'info';
        if (statusCode >= 400 && statusCode < 500) {
            logLevel = 'warn';
        }
        else if (statusCode >= 500) {
            logLevel = 'error';
        }
        (0, logger_1.Log)('backend', logLevel, 'middleware', `${method} ${url} - ${statusCode} - ${duration}ms - IP: ${ip}`);
    });
    next();
};
exports.requestLogger = requestLogger;
const securityHeaders = (req, res, next) => {
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    (0, logger_1.Log)('backend', 'debug', 'middleware', 'Security headers applied');
    next();
};
exports.securityHeaders = securityHeaders;
//# sourceMappingURL=requestLogger.js.map