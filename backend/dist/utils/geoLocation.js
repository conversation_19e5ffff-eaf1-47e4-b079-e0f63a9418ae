"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GeoLocationService = void 0;
const logger_1 = require("../logging_middleware/logger");
class GeoLocationService {
    static getLocationFromIP(ipAddress) {
        try {
            const cleanIP = ipAddress.replace(/^::ffff:/, '').split(':')[0];
            if (this.isLocalOrPrivateIP(cleanIP)) {
                return 'Local/Private Network';
            }
            const location = this.getMockLocationFromIP(cleanIP);
            (0, logger_1.Log)('backend', 'debug', 'utils', `IP ${cleanIP} mapped to location: ${location}`);
            return location;
        }
        catch (error) {
            (0, logger_1.Log)('backend', 'warn', 'utils', `Failed to get location for IP ${ipAddress}: ${error}`);
            return 'Unknown';
        }
    }
    static isLocalOrPrivateIP(ip) {
        if (!ip || ip === '127.0.0.1' || ip === 'localhost' || ip === '::1') {
            return true;
        }
        const privateRanges = [
            /^10\./,
            /^172\.(1[6-9]|2[0-9]|3[0-1])\./,
            /^192\.168\./,
            /^169\.254\./,
        ];
        return privateRanges.some(range => range.test(ip));
    }
    static getMockLocationFromIP(ip) {
        const ipParts = ip.split('.').map(Number);
        if (ipParts.length !== 4) {
            return 'Unknown';
        }
        const firstOctet = ipParts[0];
        const secondOctet = ipParts[1];
        if (firstOctet >= 1 && firstOctet <= 50) {
            return 'North America';
        }
        else if (firstOctet >= 51 && firstOctet <= 100) {
            return 'Europe';
        }
        else if (firstOctet >= 101 && firstOctet <= 150) {
            return 'Asia';
        }
        else if (firstOctet >= 151 && firstOctet <= 200) {
            return 'South America';
        }
        else if (firstOctet >= 201 && firstOctet <= 230) {
            return 'Africa';
        }
        else {
            return 'Oceania';
        }
    }
    static getDetailedLocation(ipAddress) {
        const baseLocation = this.getLocationFromIP(ipAddress);
        return {
            country: baseLocation,
            region: 'Unknown Region',
            city: 'Unknown City'
        };
    }
}
exports.GeoLocationService = GeoLocationService;
//# sourceMappingURL=geoLocation.js.map