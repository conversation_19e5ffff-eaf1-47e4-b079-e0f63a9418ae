"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ShortCodeGenerator = void 0;
const nanoid_1 = require("nanoid");
const logger_1 = require("../logging_middleware/logger");
class ShortCodeGenerator {
    static generateRandomCode(length = this.DEFAULT_LENGTH) {
        try {
            const code = (0, nanoid_1.nanoid)(length);
            (0, logger_1.Log)('backend', 'debug', 'utils', `Generated random short code: ${code}`);
            return code;
        }
        catch (error) {
            (0, logger_1.Log)('backend', 'error', 'utils', `Failed to generate random code: ${error}`);
            throw new Error('Failed to generate short code');
        }
    }
    static validateCustomCode(code) {
        if (!code) {
            return { isValid: false, error: 'Short code cannot be empty' };
        }
        if (code.length < this.MIN_LENGTH) {
            return { isValid: false, error: `Short code must be at least ${this.MIN_LENGTH} characters long` };
        }
        if (code.length > this.MAX_LENGTH) {
            return { isValid: false, error: `Short code cannot exceed ${this.MAX_LENGTH} characters` };
        }
        const alphanumericRegex = /^[a-zA-Z0-9]+$/;
        if (!alphanumericRegex.test(code)) {
            return { isValid: false, error: 'Short code can only contain alphanumeric characters' };
        }
        const reservedWords = ['api', 'admin', 'www', 'app', 'stats', 'analytics', 'health'];
        if (reservedWords.includes(code.toLowerCase())) {
            return { isValid: false, error: 'Short code cannot be a reserved word' };
        }
        (0, logger_1.Log)('backend', 'debug', 'utils', `Custom short code validated: ${code}`);
        return { isValid: true };
    }
    static async generateUniqueCode(checkUniqueness, customCode, maxRetries = 10) {
        try {
            if (customCode) {
                const validation = this.validateCustomCode(customCode);
                if (!validation.isValid) {
                    throw new Error(validation.error);
                }
                const isUnique = await checkUniqueness(customCode);
                if (!isUnique) {
                    throw new Error('Custom short code is already in use');
                }
                (0, logger_1.Log)('backend', 'info', 'utils', `Using custom short code: ${customCode}`);
                return customCode;
            }
            for (let attempt = 1; attempt <= maxRetries; attempt++) {
                const code = this.generateRandomCode();
                const isUnique = await checkUniqueness(code);
                if (isUnique) {
                    (0, logger_1.Log)('backend', 'info', 'utils', `Generated unique short code: ${code} (attempt ${attempt})`);
                    return code;
                }
                (0, logger_1.Log)('backend', 'warn', 'utils', `Short code collision detected: ${code} (attempt ${attempt})`);
            }
            throw new Error('Failed to generate unique short code after maximum retries');
        }
        catch (error) {
            (0, logger_1.Log)('backend', 'error', 'utils', `Error generating unique code: ${error}`);
            throw error;
        }
    }
}
exports.ShortCodeGenerator = ShortCodeGenerator;
ShortCodeGenerator.ALPHABET = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
ShortCodeGenerator.DEFAULT_LENGTH = 6;
ShortCodeGenerator.MAX_LENGTH = 20;
ShortCodeGenerator.MIN_LENGTH = 3;
//# sourceMappingURL=shortCodeGenerator.js.map