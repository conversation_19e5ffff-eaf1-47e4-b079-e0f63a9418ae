"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UrlController = void 0;
const UrlService_1 = require("../services/UrlService");
const logger_1 = require("../logging_middleware/logger");
class UrlController {
    constructor() {
        this.createShortUrl = async (req, res) => {
            try {
                (0, logger_1.Log)('backend', 'info', 'controller', `Received URL shortening request from ${req.ip}`);
                const { url, shortcode, validity } = req.body;
                if (!url) {
                    const errorResponse = {
                        success: false,
                        error: 'MISSING_URL',
                        message: 'URL is required'
                    };
                    (0, logger_1.Log)('backend', 'warn', 'controller', 'URL shortening request missing URL parameter');
                    res.status(400).json(errorResponse);
                    return;
                }
                const result = await this.urlService.createShortUrl({
                    url,
                    shortcode,
                    validity
                });
                (0, logger_1.Log)('backend', 'info', 'controller', `URL shortened successfully`);
                res.status(201).json(result);
            }
            catch (error) {
                (0, logger_1.Log)('backend', 'error', 'controller', `URL shortening failed: ${error}`);
                const errorResponse = {
                    success: false,
                    error: 'SHORTENING_FAILED',
                    message: error instanceof Error ? error.message : 'Failed to create short URL'
                };
                let statusCode = 500;
                if (error instanceof Error) {
                    if (error.message.includes('already in use') || error.message.includes('collision')) {
                        statusCode = 409;
                    }
                    else if (error.message.includes('Invalid') || error.message.includes('must be')) {
                        statusCode = 400;
                    }
                }
                res.status(statusCode).json(errorResponse);
            }
        };
        this.redirectToOriginalUrl = async (req, res) => {
            try {
                const { shortCode } = req.params;
                (0, logger_1.Log)('backend', 'info', 'controller', `Redirect request for short code: ${shortCode}`);
                if (!shortCode) {
                    const errorResponse = {
                        success: false,
                        error: 'MISSING_SHORT_CODE',
                        message: 'Short code is required'
                    };
                    res.status(400).json(errorResponse);
                    return;
                }
                const clientInfo = {
                    ip: req.ip || req.connection.remoteAddress,
                    userAgent: req.get('User-Agent'),
                    referrer: req.get('Referer') || req.get('Referrer')
                };
                const originalUrl = await this.urlService.getOriginalUrl(shortCode, clientInfo);
                (0, logger_1.Log)('backend', 'info', 'controller', `Redirecting ${shortCode} to ${originalUrl}`);
                res.redirect(302, originalUrl);
            }
            catch (error) {
                (0, logger_1.Log)('backend', 'error', 'controller', `Redirect failed for ${req.params.shortCode}: ${error}`);
                const errorResponse = {
                    success: false,
                    error: 'REDIRECT_FAILED',
                    message: error instanceof Error ? error.message : 'Failed to redirect'
                };
                let statusCode = 500;
                if (error instanceof Error) {
                    if (error.message.includes('not found')) {
                        statusCode = 404;
                    }
                    else if (error.message.includes('expired') || error.message.includes('inactive')) {
                        statusCode = 410;
                    }
                }
                res.status(statusCode).json(errorResponse);
            }
        };
        this.getUrlStats = async (req, res) => {
            try {
                const { shortCode } = req.params;
                (0, logger_1.Log)('backend', 'info', 'controller', `Stats request for short code: ${shortCode}`);
                if (!shortCode) {
                    const errorResponse = {
                        success: false,
                        error: 'MISSING_SHORT_CODE',
                        message: 'Short code is required'
                    };
                    res.status(400).json(errorResponse);
                    return;
                }
                const stats = await this.urlService.getUrlStats(shortCode);
                (0, logger_1.Log)('backend', 'info', 'controller', `Stats retrieved for ${shortCode}`);
                res.status(200).json(stats);
            }
            catch (error) {
                (0, logger_1.Log)('backend', 'error', 'controller', `Stats retrieval failed for ${req.params.shortCode}: ${error}`);
                const errorResponse = {
                    success: false,
                    error: 'STATS_RETRIEVAL_FAILED',
                    message: error instanceof Error ? error.message : 'Failed to retrieve statistics'
                };
                const statusCode = error instanceof Error && error.message.includes('not found') ? 404 : 500;
                res.status(statusCode).json(errorResponse);
            }
        };
        this.healthCheck = async (req, res) => {
            try {
                (0, logger_1.Log)('backend', 'debug', 'controller', 'Health check requested');
                res.status(200).json({
                    success: true,
                    message: 'URL Shortener service is healthy',
                    timestamp: new Date().toISOString(),
                    version: '1.0.0'
                });
            }
            catch (error) {
                (0, logger_1.Log)('backend', 'error', 'controller', `Health check failed: ${error}`);
                res.status(500).json({
                    success: false,
                    error: 'HEALTH_CHECK_FAILED',
                    message: 'Service is unhealthy'
                });
            }
        };
        this.urlService = new UrlService_1.UrlService();
    }
}
exports.UrlController = UrlController;
//# sourceMappingURL=UrlController.js.map