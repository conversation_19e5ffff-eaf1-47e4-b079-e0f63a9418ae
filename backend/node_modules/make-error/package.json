{"name": "make-error", "version": "1.3.6", "main": "index.js", "license": "ISC", "description": "Make your own error types!", "keywords": ["create", "custom", "derive", "error", "errors", "extend", "extending", "extension", "factory", "inherit", "make", "subclass"], "homepage": "https://github.com/JsCommunity/make-error", "bugs": "https://github.com/JsCommunity/make-error/issues", "author": "<PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "git://github.com/JsCommunity/make-error.git"}, "devDependencies": {"browserify": "^16.2.3", "eslint": "^6.5.1", "eslint-config-prettier": "^6.4.0", "eslint-config-standard": "^14.1.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-node": "^10.0.0", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-standard": "^4.0.0", "husky": "^3.0.9", "jest": "^24", "prettier": "^1.14.3", "uglify-js": "^3.3.2"}, "jest": {"testEnvironment": "node"}, "scripts": {"dev-test": "jest --watch", "format": "prettier --write '**'", "prepublishOnly": "mkdir -p dist && browserify -s makeError index.js | uglifyjs -c > dist/make-error.js", "pretest": "eslint --ignore-path .gitignore .", "test": "jest"}, "files": ["dist/", "index.js", "index.d.ts"], "husky": {"hooks": {"commit-msg": "npm run test"}}}