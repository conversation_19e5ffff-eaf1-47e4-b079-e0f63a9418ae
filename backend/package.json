{"name": "url-shortener-backend", "version": "1.0.0", "description": "HTTP URL Shortener Microservice", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon --exec ts-node src/index.ts", "test": "echo \"Error: no test specified\" && exit 1", "clean": "rm -rf dist"}, "keywords": ["url-shortener", "microservice", "api"], "author": "<PERSON><PERSON><PERSON><PERSON> singh rajput", "license": "ISC", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "morgan": "^1.10.1", "nanoid": "^5.1.5", "sqlite3": "^5.1.7", "uuid": "^11.1.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "^24.0.14", "@types/uuid": "^10.0.0", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}